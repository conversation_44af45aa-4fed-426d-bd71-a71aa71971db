# 示波器驱动器差异配置使用说明

## 概述

为了支持不同驱动器的采集通道差异，系统现在支持基于驱动器名称和版本的XML配置文件。系统会根据当前连接的驱动器信息自动选择最合适的配置文件。

## 配置文件命名规则

配置文件按以下优先级进行匹配：

1. **驱动器名称 + 软件版本**: `OscilloscopeOptions_{驱动器名称}_V{版本号}.xml`
   - 例如: `OscilloscopeOptions_RS60E_V3000.xml`

2. **驱动器名称 + 硬件版本**: `OscilloscopeOptions_{驱动器名称}_HW{硬件版本}.xml`
   - 例如: `OscilloscopeOptions_RS60E_HW2024.xml`

3. **仅驱动器名称**: `OscilloscopeOptions_{驱动器名称}.xml`
   - 例如: `OscilloscopeOptions_RS60E.xml`

4. **仅软件版本**: `OscilloscopeOptions_V{版本号}.xml`
   - 例如: `OscilloscopeOptions_V3000.xml`

5. **默认配置**: `OscilloscopeOptions.xml`

## 配置文件结构

```xml
<?xml version="1.0" encoding="utf-8" ?>
<!-- 驱动器专用示波器配置文件 -->
<OscilloscopeOptions>
  <SamplingPeriods>
    <Period>62.5μs</Period>
    <Period>125μs</Period>
    <!-- 更多采样周期... -->
  </SamplingPeriods>
  <SampleChannels>
    <Channel GroupName="位置" ID="1" ItemName="位置指令" Address="03" />
    <Channel GroupName="速度" ID="7" ItemName="速度指令" Address="07" />
    <!-- 更多通道配置... -->
  </SampleChannels>
</OscilloscopeOptions>
```

### 配置项说明

#### SamplingPeriods（采样周期）
- `<Period>`: 定义可用的采样周期
- 支持μs（微秒）和ms（毫秒）单位
- 不同驱动器可以有不同的采样周期选项

#### SampleChannels（采样通道）
- `GroupName`: 通道分组名称（如"位置"、"速度"、"转矩"等）
- `ID`: 通道唯一标识符
- `ItemName`: 通道显示名称
- `Address`: 通道对应的硬件地址（十六进制）

## 使用方法

### 1. 为新驱动器创建配置文件

#### 方法一：复制现有配置文件
```csharp
// 从默认配置复制并修改
string sourcePath = @"C:\ServoStudio\Xml\OscilloscopeOptions.xml";
string targetServoName = "NewServo";

// 地址映射（如果需要）
Dictionary<string, string> addressMapping = new Dictionary<string, string>
{
    {"03", "30"}, // 位置指令地址从03改为30
    {"07", "40"}  // 速度指令地址从07改为40
};

bool success = OscilloscopeConfigTemplate.CopyAndModifyConfig(
    sourcePath, targetServoName, addressMapping);
```

#### 方法二：生成新的配置模板
```csharp
// 为新驱动器生成配置模板
string servoName = "NewServo";
string version = "1000"; // 可选

bool success = OscilloscopeConfigTemplate.GenerateConfigTemplate(
    servoName, version);
```

### 2. 自定义通道配置

```csharp
// 创建自定义通道列表
List<SampleChannelInfoSet> customChannels = new List<SampleChannelInfoSet>
{
    new SampleChannelInfoSet 
    { 
        GroupName = "位置", 
        ID = 1, 
        ItemName = "位置指令", 
        Address = "30" 
    },
    new SampleChannelInfoSet 
    { 
        GroupName = "特殊功能", 
        ID = 50, 
        ItemName = "自定义参数1", 
        Address = "A0" 
    }
};

// 生成带自定义通道的配置文件
bool success = OscilloscopeConfigTemplate.GenerateConfigTemplate(
    "CustomServo", "2000", customChannels);
```

### 3. 验证和修复配置文件

```csharp
// 验证配置文件是否有效
string configPath = @"C:\ServoStudio\Xml\OscilloscopeOptions_RS60E.xml";
bool isValid = OscilloscopeConfigManager.ValidateConfigFile(configPath);

// 修复损坏的配置文件
if (!isValid)
{
    bool repaired = OscilloscopeConfigTemplate.ValidateAndRepairConfig(configPath);
}
```

### 4. 获取配置信息

```csharp
// 获取当前使用的配置文件信息
string configInfo = XmlHelper.GetCurrentConfigInfo();
Console.WriteLine(configInfo); // 输出: "使用专用配置: RS60E V3000"

// 获取所有可用的配置文件
List<ConfigFileInfo> availableConfigs = XmlHelper.GetAvailableConfigs();
foreach (var config in availableConfigs)
{
    Console.WriteLine($"{config.DisplayName}: {config.FileName}");
}
```

## 实际应用示例

### 示例1：RS60E驱动器配置

文件名: `OscilloscopeOptions_RS60E.xml`

特点：
- 支持高速采样（31.25μs）
- 包含RS60E特有的高级功能通道
- 地址映射与标准配置相同

### 示例2：Black Tiger驱动器配置

文件名: `OscilloscopeOptions_BlackTiger.xml`

特点：
- 多轴功能通道
- 网络通信状态通道
- 使用不同的地址映射

### 示例3：版本特定配置

文件名: `OscilloscopeOptions_V3000.xml`

特点：
- V3.0版本新增的高级算法通道
- 安全功能监控通道
- 支持更高精度的采样

## 注意事项

1. **文件位置**: 所有配置文件必须放在 `ServoStudio\Xml\` 目录下

2. **编码格式**: 配置文件必须使用UTF-8编码

3. **地址格式**: 通道地址使用十六进制格式，不需要"0x"前缀

4. **ID唯一性**: 每个配置文件内的通道ID必须唯一

5. **向后兼容**: 如果没有找到专用配置文件，系统会自动使用默认配置

6. **实时切换**: 配置文件的选择在连接驱动器时自动进行，无需重启软件

## 故障排除

### 问题1：配置文件不生效
- 检查文件名是否符合命名规则
- 确认驱动器名称和版本信息是否正确
- 验证XML文件格式是否正确

### 问题2：通道显示异常
- 检查通道地址是否正确
- 确认ID是否重复
- 验证GroupName和ItemName是否为空

### 问题3：采样周期选项缺失
- 检查SamplingPeriods节点是否存在
- 确认Period格式是否正确（包含单位）

## 扩展功能

系统还提供了以下扩展功能：

1. **配置文件模板生成器**: 快速为新驱动器生成配置文件
2. **配置文件验证器**: 检查配置文件的完整性和正确性
3. **配置文件修复工具**: 自动修复损坏的配置文件
4. **配置信息查询**: 获取当前使用的配置文件信息

通过这些功能，可以更方便地管理不同驱动器的配置差异，提高系统的灵活性和可维护性。
