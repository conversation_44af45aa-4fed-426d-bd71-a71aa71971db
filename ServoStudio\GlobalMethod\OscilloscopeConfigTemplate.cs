using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalVariable;
using ServoStudio.Models;

namespace ServoStudio.GlobalMethod
{
    /// <summary>
    /// 示波器配置模板生成器
    /// 用于为新的驱动器类型生成配置文件模板
    /// </summary>
    public static class OscilloscopeConfigTemplate
    {
        /// <summary>
        /// 为指定驱动器生成配置文件模板
        /// </summary>
        /// <param name="servoName">驱动器名称</param>
        /// <param name="version">版本号</param>
        /// <param name="customChannels">自定义通道列表</param>
        /// <returns>是否成功</returns>
        public static bool GenerateConfigTemplate(string servoName, string version = null, List<SampleChannelInfoSet> customChannels = null)
        {
            try
            {
                string fileName = $"OscilloscopeOptions_{servoName}";
                if (!string.IsNullOrEmpty(version))
                {
                    fileName += $"_V{version}";
                }
                fileName += ".xml";
                
                string filePath = Path.Combine(System.Environment.CurrentDirectory, "Xml", fileName);
                
                // 创建XML文档
                XmlDocument doc = new XmlDocument();
                
                // 添加XML声明
                XmlDeclaration declaration = doc.CreateXmlDeclaration("1.0", "utf-8", null);
                doc.AppendChild(declaration);
                
                // 添加注释
                XmlComment comment = doc.CreateComment($" {servoName}驱动器专用示波器配置文件 ");
                doc.AppendChild(comment);
                
                // 创建根节点
                XmlElement root = doc.CreateElement("OscilloscopeOptions");
                doc.AppendChild(root);
                
                // 添加采样周期配置
                AddSamplingPeriods(doc, root);
                
                // 添加通道配置
                if (customChannels != null && customChannels.Count > 0)
                {
                    AddCustomChannels(doc, root, customChannels);
                }
                else
                {
                    AddDefaultChannels(doc, root);
                }
                
                // 保存文件
                doc.Save(filePath);
                
                return true;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GenerateConfigTemplate", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 添加采样周期配置
        /// </summary>
        private static void AddSamplingPeriods(XmlDocument doc, XmlElement root)
        {
            XmlElement samplingPeriods = doc.CreateElement("SamplingPeriods");
            root.AppendChild(samplingPeriods);
            
            // 标准采样周期
            string[] periods = {
                "62.5μs", "125μs", "250μs", "500μs",
                "1ms", "2ms", "4ms", "8ms"
            };
            
            foreach (string period in periods)
            {
                XmlElement periodElement = doc.CreateElement("Period");
                periodElement.InnerText = period;
                samplingPeriods.AppendChild(periodElement);
            }
        }
        
        /// <summary>
        /// 添加默认通道配置
        /// </summary>
        private static void AddDefaultChannels(XmlDocument doc, XmlElement root)
        {
            XmlElement sampleChannels = doc.CreateElement("SampleChannels");
            root.AppendChild(sampleChannels);
            
            // 基础通道配置
            var defaultChannels = GetDefaultChannelConfig();
            
            foreach (var channel in defaultChannels)
            {
                XmlElement channelElement = doc.CreateElement("Channel");
                channelElement.SetAttribute("GroupName", channel.GroupName);
                channelElement.SetAttribute("ID", channel.ID.ToString());
                channelElement.SetAttribute("ItemName", channel.ItemName);
                channelElement.SetAttribute("Address", channel.Address);
                sampleChannels.AppendChild(channelElement);
            }
        }
        
        /// <summary>
        /// 添加自定义通道配置
        /// </summary>
        private static void AddCustomChannels(XmlDocument doc, XmlElement root, List<SampleChannelInfoSet> customChannels)
        {
            XmlElement sampleChannels = doc.CreateElement("SampleChannels");
            root.AppendChild(sampleChannels);
            
            foreach (var channel in customChannels)
            {
                XmlElement channelElement = doc.CreateElement("Channel");
                channelElement.SetAttribute("GroupName", channel.GroupName);
                channelElement.SetAttribute("ID", channel.ID.ToString());
                channelElement.SetAttribute("ItemName", channel.ItemName);
                channelElement.SetAttribute("Address", channel.Address);
                sampleChannels.AppendChild(channelElement);
            }
        }
        
        /// <summary>
        /// 获取默认通道配置
        /// </summary>
        private static List<SampleChannelInfoSet> GetDefaultChannelConfig()
        {
            return new List<SampleChannelInfoSet>
            {
                // 基础通道
                new SampleChannelInfoSet { GroupName = "停用", ID = 0, ItemName = "停用", Address = "" },
                
                // 位置相关通道
                new SampleChannelInfoSet { GroupName = "位置", ID = 1, ItemName = "位置指令", Address = "03" },
                new SampleChannelInfoSet { GroupName = "位置", ID = 2, ItemName = "滤波后位置指令", Address = "1A" },
                new SampleChannelInfoSet { GroupName = "位置", ID = 3, ItemName = "位置反馈", Address = "04" },
                new SampleChannelInfoSet { GroupName = "位置", ID = 4, ItemName = "位置差值", Address = "05" },
                
                // 速度相关通道
                new SampleChannelInfoSet { GroupName = "速度", ID = 7, ItemName = "位置环输出速度指令", Address = "06" },
                new SampleChannelInfoSet { GroupName = "速度", ID = 8, ItemName = "速度指令", Address = "07" },
                new SampleChannelInfoSet { GroupName = "速度", ID = 10, ItemName = "速度反馈", Address = "08" },
                new SampleChannelInfoSet { GroupName = "速度", ID = 13, ItemName = "速度差值", Address = "09" },
                
                // 转矩相关通道
                new SampleChannelInfoSet { GroupName = "转矩", ID = 14, ItemName = "速度环输出转矩指令", Address = "0A" },
                new SampleChannelInfoSet { GroupName = "转矩", ID = 15, ItemName = "转矩指令", Address = "0B" },
                new SampleChannelInfoSet { GroupName = "转矩", ID = 16, ItemName = "转矩反馈", Address = "0C" },
                new SampleChannelInfoSet { GroupName = "转矩", ID = 17, ItemName = "U相电流", Address = "0D" },
                new SampleChannelInfoSet { GroupName = "转矩", ID = 18, ItemName = "V相电流", Address = "0E" },
                new SampleChannelInfoSet { GroupName = "转矩", ID = 19, ItemName = "W相电流", Address = "0F" },
                
                // 母线相关通道
                new SampleChannelInfoSet { GroupName = "母线", ID = 31, ItemName = "母线电压", Address = "01" },
                new SampleChannelInfoSet { GroupName = "母线", ID = 32, ItemName = "母线电流", Address = "02" },
                
                // Debug通道
                new SampleChannelInfoSet { GroupName = "Debug", ID = 38, ItemName = "Debug参数1", Address = "26" },
                new SampleChannelInfoSet { GroupName = "Debug", ID = 39, ItemName = "Debug参数2", Address = "27" },
                new SampleChannelInfoSet { GroupName = "Debug", ID = 40, ItemName = "Debug参数3", Address = "28" },
                new SampleChannelInfoSet { GroupName = "Debug", ID = 41, ItemName = "Debug参数4", Address = "29" }
            };
        }
        
        /// <summary>
        /// 从现有配置文件复制并修改
        /// </summary>
        /// <param name="sourceConfigPath">源配置文件路径</param>
        /// <param name="targetServoName">目标驱动器名称</param>
        /// <param name="addressMapping">地址映射字典</param>
        /// <returns>是否成功</returns>
        public static bool CopyAndModifyConfig(string sourceConfigPath, string targetServoName, Dictionary<string, string> addressMapping = null)
        {
            try
            {
                if (!File.Exists(sourceConfigPath))
                {
                    return false;
                }
                
                // 加载源配置文件
                XmlDocument sourceDoc = new XmlDocument();
                sourceDoc.Load(sourceConfigPath);
                
                // 创建新的配置文件
                XmlDocument targetDoc = new XmlDocument();
                targetDoc.LoadXml(sourceDoc.OuterXml);
                
                // 修改注释
                foreach (XmlNode node in targetDoc.ChildNodes)
                {
                    if (node.NodeType == XmlNodeType.Comment)
                    {
                        node.Value = $" {targetServoName}驱动器专用示波器配置文件 ";
                        break;
                    }
                }
                
                // 如果提供了地址映射，则更新地址
                if (addressMapping != null && addressMapping.Count > 0)
                {
                    XmlNodeList channelNodes = targetDoc.SelectNodes("//Channel[@Address]");
                    foreach (XmlNode channelNode in channelNodes)
                    {
                        string currentAddress = channelNode.Attributes["Address"].Value;
                        if (addressMapping.ContainsKey(currentAddress))
                        {
                            channelNode.Attributes["Address"].Value = addressMapping[currentAddress];
                        }
                    }
                }
                
                // 保存新配置文件
                string targetPath = Path.Combine(
                    Path.GetDirectoryName(sourceConfigPath),
                    $"OscilloscopeOptions_{targetServoName}.xml"
                );
                
                targetDoc.Save(targetPath);
                
                return true;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "CopyAndModifyConfig", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 验证并修复配置文件
        /// </summary>
        /// <param name="configPath">配置文件路径</param>
        /// <returns>是否成功</returns>
        public static bool ValidateAndRepairConfig(string configPath)
        {
            try
            {
                if (!File.Exists(configPath))
                {
                    return false;
                }
                
                XmlDocument doc = new XmlDocument();
                doc.Load(configPath);
                
                bool needRepair = false;
                
                // 检查并修复根节点
                XmlNode rootNode = doc.SelectSingleNode("OscilloscopeOptions");
                if (rootNode == null)
                {
                    return false; // 根节点缺失，无法修复
                }
                
                // 检查并修复SamplingPeriods节点
                XmlNode samplingPeriodsNode = rootNode.SelectSingleNode("SamplingPeriods");
                if (samplingPeriodsNode == null)
                {
                    samplingPeriodsNode = doc.CreateElement("SamplingPeriods");
                    rootNode.AppendChild(samplingPeriodsNode);
                    AddSamplingPeriods(doc, (XmlElement)rootNode);
                    needRepair = true;
                }
                
                // 检查并修复SampleChannels节点
                XmlNode sampleChannelsNode = rootNode.SelectSingleNode("SampleChannels");
                if (sampleChannelsNode == null)
                {
                    sampleChannelsNode = doc.CreateElement("SampleChannels");
                    rootNode.AppendChild(sampleChannelsNode);
                    AddDefaultChannels(doc, (XmlElement)rootNode);
                    needRepair = true;
                }
                
                // 如果需要修复，保存文件
                if (needRepair)
                {
                    doc.Save(configPath);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "ValidateAndRepairConfig", ex);
                return false;
            }
        }
    }
}
