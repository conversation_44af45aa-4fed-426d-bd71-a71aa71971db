﻿using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ServoStudio.GlobalMethod
{
    public static class XmlHelper
    {
        public static XmlDocument xmlDoc = null;
        public static XmlDocument xmlDocOptions = null;
        //*************************************************************************
        //函数名称：XmlOscilloscopePresetConfigs
        //函数功能：加载示波器预配置XML文件
        //
        //输入参数：
        //         
        //        
        //        
        //
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.24
        //*************************************************************************
        public static void XmlOscilloscopePresetConfigs()
        {
            try
            {
                //FilePath.XmlPath = FilePath.XmlPath.Substring(0, FilePath.XmlPath.Substring(0, FilePath.XmlPath.Substring(0, FilePath.XmlPath.LastIndexOf("\\")).LastIndexOf("\\")).LastIndexOf("\\")) + "\\Xml\\OscilloscopePresetConfigs.xml";
                xmlDoc = new XmlDocument();
                xmlDoc.Load(FilePath.XmlPath);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "OscilloscopePresetConfigs", ex);
            }
        }
        //*************************************************************************
        //函数名称：GetOscilloscopePresetConfigsData
        //函数功能：获取示波器预配置XML文件数据
        //
        //输入参数：
        //         
        //        
        //        
        //
        //输出参数：返回示波器预配置XML文件数据集合
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.24
        //*************************************************************************
        public static List<OscilloscopePresetModel> GetOscilloscopePresetConfigsData()
        {
            List<OscilloscopePresetModel> oscilloscopePresets = new List<OscilloscopePresetModel>();
            string xPath = "OscilloscopePresetConfigs/config";
            XmlNodeList xmlNodeList = xmlDoc.SelectNodes(xPath);
            foreach (XmlNode xNode in xmlNodeList)
            {
                OscilloscopePresetModel oscilloscopePreset = new OscilloscopePresetModel
                {
                    Id = xNode.Attributes["Id"].Value,
                    SelectedSampleChannel1Index = xNode.Attributes["SelectedSampleChannel1Index"].Value.ChangeInt(),
                    SelectedSampleChannel2Index = xNode.Attributes["SelectedSampleChannel2Index"].Value.ChangeInt(),
                    SelectedSampleChannel3Index = xNode.Attributes["SelectedSampleChannel3Index"].Value.ChangeInt(),
                    SelectedSampleChannel4Index = xNode.Attributes["SelectedSampleChannel4Index"].Value.ChangeInt(),
                    SamplingPeriod = xNode.Attributes["SamplingPeriod"].Value,
                    SamplingDuration = xNode.Attributes["SamplingDuration"].Value,
                    ContinuousSampling = xNode.Attributes["ContinuousSampling"].Value,
                    TriggerClockEdge = xNode.Attributes["TriggerClockEdge"].Value,
                    TriggerChannel = xNode.Attributes["TriggerChannel"].Value,
                    PreTrigger = xNode.Attributes["PreTrigger"].Value,
                    TriggerLevel = xNode.Attributes["TriggerLevel"].Value
                };
                oscilloscopePresets.Add(oscilloscopePreset);
            }
            return oscilloscopePresets;
        }
        //*************************************************************************
        //函数名称：SaveToOscilloscopePresetConfigs
        //函数功能：保存示波器预配置XML文件数据
        //
        //输入参数：oscilloscopePresetModel  示波器预配置XML文件Model
        //         
        //        
        //        
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        public static int SaveToOscilloscopePresetConfigs(OscilloscopePresetModel oscilloscopePresetModel)
        {
            int iRet = -1;
            try
            {
                //第一步，创建元素
                XmlElement configElement = xmlDoc.CreateElement("config");
                //第二步，创建指定节点
                XmlNode xmlNode = xmlDoc.SelectSingleNode("OscilloscopePresetConfigs");
                //第三步，给元素赋值
                configElement.SetAttribute("Id", oscilloscopePresetModel.Id.ToString());
                configElement.SetAttribute("SelectedSampleChannel1Index", oscilloscopePresetModel.SelectedSampleChannel1Index.ToString());
                configElement.SetAttribute("SelectedSampleChannel2Index", oscilloscopePresetModel.SelectedSampleChannel2Index.ToString());
                configElement.SetAttribute("SelectedSampleChannel3Index", oscilloscopePresetModel.SelectedSampleChannel3Index.ToString());
                configElement.SetAttribute("SelectedSampleChannel4Index", oscilloscopePresetModel.SelectedSampleChannel4Index.ToString());
                configElement.SetAttribute("SamplingPeriod", oscilloscopePresetModel.SamplingPeriod);
                configElement.SetAttribute("SamplingDuration", oscilloscopePresetModel.SamplingDuration);
                configElement.SetAttribute("ContinuousSampling", oscilloscopePresetModel.ContinuousSampling);
                configElement.SetAttribute("TriggerClockEdge", oscilloscopePresetModel.TriggerClockEdge);
                configElement.SetAttribute("TriggerChannel", oscilloscopePresetModel.TriggerChannel);
                configElement.SetAttribute("PreTrigger", oscilloscopePresetModel.PreTrigger);
                configElement.SetAttribute("TriggerLevel", oscilloscopePresetModel.TriggerLevel);
                xmlNode.AppendChild(configElement);
                xmlDoc.Save(FilePath.XmlPath);
                iRet = RET.SUCCEEDED;
            }
            catch 
            {
                iRet = RET.ERROR;
            }
            return iRet;
        }
        //*************************************************************************
        //函数名称：EditOscilloscopePresetConfigs
        //函数功能：编辑示波器预配置XML文件数据
        //
        //输入参数：oscilloscopePresetModel  示波器预配置XML文件Model
        //         
        //        
        //        
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        public static int EditOscilloscopePresetConfigs(OscilloscopePresetModel oscilloscopePresetModel)
        {
            int iRet = -1;
            try
            {
                string oscilloscopePresetId = oscilloscopePresetModel.Id;
                if (oscilloscopePresetId != null)
                {
                    string xPath = string.Format("/OscilloscopePresetConfigs/config[@Id='{0}']", oscilloscopePresetId);
                    XmlNode editXmlNode = xmlDoc.SelectSingleNode(xPath);
                    XmlElement editXmlElement = editXmlNode as XmlElement;
                    if (!(editXmlElement == null))
                    {
                        editXmlElement.Attributes["SelectedSampleChannel1Index"].Value = oscilloscopePresetModel.SelectedSampleChannel1Index.ToString();
                        editXmlElement.Attributes["SelectedSampleChannel2Index"].Value = oscilloscopePresetModel.SelectedSampleChannel2Index.ToString();
                        editXmlElement.Attributes["SelectedSampleChannel3Index"].Value = oscilloscopePresetModel.SelectedSampleChannel3Index.ToString();
                        editXmlElement.Attributes["SelectedSampleChannel4Index"].Value = oscilloscopePresetModel.SelectedSampleChannel4Index.ToString();
                        editXmlElement.Attributes["SamplingPeriod"].Value = oscilloscopePresetModel.SamplingPeriod;
                        editXmlElement.Attributes["SamplingDuration"].Value = oscilloscopePresetModel.SamplingDuration;
                        editXmlElement.Attributes["ContinuousSampling"].Value = oscilloscopePresetModel.ContinuousSampling;
                        editXmlElement.Attributes["TriggerClockEdge"].Value = oscilloscopePresetModel.TriggerClockEdge;
                        editXmlElement.Attributes["TriggerChannel"].Value = oscilloscopePresetModel.TriggerChannel;
                        if (oscilloscopePresetModel.PreTrigger == "0")
                        {
                            editXmlElement.Attributes["PreTrigger"].Value = oscilloscopePresetModel.PreTrigger;
                        }
                        else
                        {
                            editXmlElement.Attributes["PreTrigger"].Value = oscilloscopePresetModel.PreTrigger.Substring(0, 2);
                        }                        
                        editXmlElement.Attributes["TriggerLevel"].Value = oscilloscopePresetModel.TriggerLevel;                       
                        xmlDoc.Save(FilePath.XmlPath);//最后保存一下XML文件
                        iRet = RET.SUCCEEDED;
                    }
                }               
            }
            catch
            {
                iRet = RET.ERROR;
            }
            return iRet;
        }

        public static void XmlOscilloscopeOptions()
        {
            try
            {
                xmlDocOptions = new XmlDocument();
                string xmlPath = GetOscilloscopeOptionsPath();

                // 检查文件是否存在
                if (!System.IO.File.Exists(xmlPath))
                {
                    xmlDocOptions = null;
                    return;
                }

                xmlDocOptions.Load(xmlPath);
            }
            catch (System.Exception ex)
            {
                xmlDocOptions = null;
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "XmlOscilloscopeOptions", ex);
            }
        }

        /// <summary>
        /// 根据驱动器信息获取对应的示波器配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        private static string GetOscilloscopeOptionsPath()
        {
            // 使用统一的配置文件
            return GlobalConstant.FilePath.XmlOptionsPath;
        }

        /// <summary>
        /// 获取当前使用的配置信息
        /// </summary>
        /// <returns>配置信息</returns>
        public static string GetCurrentConfigInfo()
        {
            try
            {
                string servoName = SoftwareStateParameterSet.ServoName;
                string softwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion;

                if (string.IsNullOrEmpty(servoName))
                {
                    return "使用默认配置";
                }

                // 检查是否有匹配的驱动器配置
                var matchedConfig = GetMatchedServoConfig();
                if (matchedConfig != null)
                {
                    return $"使用专用配置: {servoName}";
                }

                return "使用默认配置";
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetCurrentConfigInfo", ex);
                return "配置信息获取失败";
            }
        }

        /// <summary>
        /// 获取匹配的驱动器配置节点
        /// </summary>
        /// <returns>匹配的配置节点</returns>
        private static XmlNode GetMatchedServoConfig()
        {
            try
            {
                if (xmlDocOptions == null)
                {
                    XmlOscilloscopeOptions();
                    if (xmlDocOptions == null) return null;
                }

                string servoName = SoftwareStateParameterSet.ServoName?.Trim();
                string softwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion?.Trim();
                string hardwareVersion = SoftwareStateParameterSet.ServoHardwareVersion?.Trim();

                XmlNodeList servoConfigs = xmlDocOptions.SelectNodes("OscilloscopeOptions/ServoConfigs/ServoConfig");
                if (servoConfigs == null) return null;

                foreach (XmlNode config in servoConfigs)
                {
                    if (IsConfigMatched(config, servoName, softwareVersion, hardwareVersion))
                    {
                        return config;
                    }
                }

                return null;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetMatchedServoConfig", ex);
                return null;
            }
        }

        /// <summary>
        /// 检查配置是否匹配
        /// </summary>
        private static bool IsConfigMatched(XmlNode config, string servoName, string softwareVersion, string hardwareVersion)
        {
            try
            {
                string configName = config.Attributes["Name"]?.Value;
                string configSoftwareVersion = config.Attributes["SoftwareVersion"]?.Value;
                string configHardwareVersion = config.Attributes["HardwareVersion"]?.Value;

                // 检查驱动器名称匹配
                if (!string.IsNullOrEmpty(configName) && configName != "*")
                {
                    if (string.IsNullOrEmpty(servoName) || !servoName.Equals(configName, StringComparison.OrdinalIgnoreCase))
                    {
                        return false;
                    }
                }

                // 检查软件版本匹配
                if (!string.IsNullOrEmpty(configSoftwareVersion) && configSoftwareVersion != "*")
                {
                    if (string.IsNullOrEmpty(softwareVersion) || !IsVersionMatched(softwareVersion, configSoftwareVersion))
                    {
                        return false;
                    }
                }

                // 检查硬件版本匹配
                if (!string.IsNullOrEmpty(configHardwareVersion) && configHardwareVersion != "*")
                {
                    if (string.IsNullOrEmpty(hardwareVersion) || !IsVersionMatched(hardwareVersion, configHardwareVersion))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查版本是否匹配（支持通配符）
        /// </summary>
        private static bool IsVersionMatched(string actualVersion, string configVersion)
        {
            try
            {
                if (configVersion.Contains("*"))
                {
                    string pattern = configVersion.Replace("*", ".*");
                    return System.Text.RegularExpressions.Regex.IsMatch(actualVersion, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                }
                else
                {
                    return actualVersion.Equals(configVersion, StringComparison.OrdinalIgnoreCase);
                }
            }
            catch
            {
                return false;
            }
        }

        public static List<string> GetSamplingPeriods()
        {
            List<string> periods = new List<string>();

            // 检查xmlDocOptions是否已正确初始化
            if (xmlDocOptions == null)
            {
                // 尝试重新初始化
                XmlOscilloscopeOptions();
                if (xmlDocOptions == null)
                {
                    // 如果仍然为null，返回空列表
                    return periods;
                }
            }

            try
            {
                // 首先尝试获取匹配的驱动器配置
                XmlNode matchedConfig = GetMatchedServoConfig();
                if (matchedConfig != null)
                {
                    XmlNode samplingPeriodsNode = matchedConfig.SelectSingleNode("SamplingPeriods");
                    if (samplingPeriodsNode != null)
                    {
                        XmlNodeList periodNodes = samplingPeriodsNode.SelectNodes("Period");
                        if (periodNodes != null)
                        {
                            foreach (XmlNode periodNode in periodNodes)
                            {
                                periods.Add(periodNode.InnerText);
                            }
                            return periods;
                        }
                    }
                }

                // 如果没有找到匹配的配置，使用默认配置
                string xPath = "OscilloscopeOptions/DefaultConfig/SamplingPeriods/Period";
                XmlNodeList xmlNodeList = xmlDocOptions.SelectNodes(xPath);
                if (xmlNodeList != null)
                {
                    foreach (XmlNode xNode in xmlNodeList)
                    {
                        periods.Add(xNode.InnerText);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetSamplingPeriods", ex);
            }

            return periods;
        }

        public static List<SampleChannelInfoSet> GetSampleChannels()
        {
            List<SampleChannelInfoSet> channels = new List<SampleChannelInfoSet>();

            // 检查xmlDocOptions是否已正确初始化
            if (xmlDocOptions == null)
            {
                // 尝试重新初始化
                XmlOscilloscopeOptions();
                if (xmlDocOptions == null)
                {
                    // 如果仍然为null，返回空列表
                    return channels;
                }
            }

            try
            {
                // 首先尝试获取匹配的驱动器配置
                XmlNode matchedConfig = GetMatchedServoConfig();
                if (matchedConfig != null)
                {
                    XmlNode sampleChannelsNode = matchedConfig.SelectSingleNode("SampleChannels");
                    if (sampleChannelsNode != null)
                    {
                        // 检查是否继承默认配置
                        XmlNode inheritDefaultNode = sampleChannelsNode.SelectSingleNode("InheritDefault");
                        bool inheritDefault = inheritDefaultNode != null &&
                                             inheritDefaultNode.InnerText.Equals("true", StringComparison.OrdinalIgnoreCase);

                        if (inheritDefault)
                        {
                            // 先加载默认通道
                            channels.AddRange(GetDefaultChannels());
                        }

                        // 加载驱动器特有的通道
                        XmlNodeList channelNodes = sampleChannelsNode.SelectNodes("Channel");
                        if (channelNodes != null)
                        {
                            foreach (XmlNode channelNode in channelNodes)
                            {
                                SampleChannelInfoSet channel = CreateChannelFromNode(channelNode);
                                if (channel != null)
                                {
                                    // 如果继承默认配置，检查是否需要覆盖现有通道
                                    if (inheritDefault)
                                    {
                                        var existingChannel = channels.FirstOrDefault(c => c.ID == channel.ID);
                                        if (existingChannel != null)
                                        {
                                            channels.Remove(existingChannel);
                                        }
                                    }
                                    channels.Add(channel);
                                }
                            }
                        }

                        return channels;
                    }
                }

                // 如果没有找到匹配的配置，使用默认配置
                channels.AddRange(GetDefaultChannels());
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetSampleChannels", ex);
            }

            return channels;
        }

        /// <summary>
        /// 获取默认通道配置
        /// </summary>
        /// <returns>默认通道列表</returns>
        private static List<SampleChannelInfoSet> GetDefaultChannels()
        {
            List<SampleChannelInfoSet> channels = new List<SampleChannelInfoSet>();

            try
            {
                string xPath = "OscilloscopeOptions/DefaultConfig/SampleChannels/Channel";
                XmlNodeList xmlNodeList = xmlDocOptions.SelectNodes(xPath);
                if (xmlNodeList != null)
                {
                    foreach (XmlNode xNode in xmlNodeList)
                    {
                        SampleChannelInfoSet channel = CreateChannelFromNode(xNode);
                        if (channel != null)
                        {
                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetDefaultChannels", ex);
            }

            return channels;
        }

        /// <summary>
        /// 从XML节点创建通道对象
        /// </summary>
        /// <param name="node">XML节点</param>
        /// <returns>通道对象</returns>
        private static SampleChannelInfoSet CreateChannelFromNode(XmlNode node)
        {
            try
            {
                if (node?.Attributes == null) return null;

                return new SampleChannelInfoSet
                {
                    GroupName = node.Attributes["GroupName"]?.Value ?? "",
                    ID = Convert.ToInt32(node.Attributes["ID"]?.Value ?? "0"),
                    ItemName = node.Attributes["ItemName"]?.Value ?? "",
                    Address = node.Attributes["Address"]?.Value ?? ""
                };
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "CreateChannelFromNode", ex);
                return null;
            }
        }
    }
}
