# 示波器配置测试示例

## 测试场景

以下是一些测试场景，用于验证单文件驱动器差异配置系统的工作情况：

## 场景1：RS60E驱动器测试

### 驱动器信息
- 驱动器名称：RS60E
- 软件版本：ServoStudio V3.0.0.0 20240101
- 硬件版本：HW2024.01.01

### 预期结果
- 匹配到RS60E专用配置
- 继承默认通道配置
- 添加RS60E特有的高级功能通道
- 支持高速采样周期（31.25μs）

### 测试代码
```csharp
// 设置测试环境
SoftwareStateParameterSet.ServoName = "RS60E";
SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V3.0.0.0 20240101";
SoftwareStateParameterSet.ServoHardwareVersion = "HW2024.01.01";

// 重新加载配置
XmlHelper.XmlOscilloscopeOptions();

// 获取采样周期
List<string> periods = XmlHelper.GetSamplingPeriods();
Console.WriteLine("采样周期：");
foreach (string period in periods)
{
    Console.WriteLine($"  - {period}");
}

// 获取通道配置
List<SampleChannelInfoSet> channels = XmlHelper.GetSampleChannels();
Console.WriteLine($"\n通道数量：{channels.Count}");

// 查找RS60E特有通道
var rs60eChannels = channels.Where(c => c.ItemName.Contains("RS60E")).ToList();
Console.WriteLine($"RS60E特有通道数量：{rs60eChannels.Count}");
foreach (var channel in rs60eChannels)
{
    Console.WriteLine($"  - {channel.GroupName}: {channel.ItemName} (地址: {channel.Address})");
}

// 获取配置信息
string configInfo = XmlHelper.GetCurrentConfigInfo();
Console.WriteLine($"\n当前配置：{configInfo}");
```

### 预期输出
```
采样周期：
  - 31.25μs
  - 62.5μs
  - 125μs
  - 250μs
  - 500μs
  - 1ms
  - 2ms
  - 4ms
  - 8ms
  - 16ms

通道数量：46
RS60E特有通道数量：2
  - Debug: RS60E专用参数1 (地址: 2A)
  - Debug: RS60E专用参数2 (地址: 2B)

当前配置：使用专用配置: RS60E
```

## 场景2：Black Tiger驱动器测试

### 驱动器信息
- 驱动器名称：Black Tiger
- 软件版本：ServoStudio V2.5.0.0 20231201
- 硬件版本：HW2023.12.01

### 预期结果
- 匹配到Black Tiger专用配置
- 不继承默认通道配置
- 使用完全自定义的地址映射
- 包含多轴功能和网络通信通道

### 测试代码
```csharp
// 设置测试环境
SoftwareStateParameterSet.ServoName = "Black Tiger";
SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V2.5.0.0 20231201";
SoftwareStateParameterSet.ServoHardwareVersion = "HW2023.12.01";

// 重新加载配置
XmlHelper.XmlOscilloscopeOptions();

// 获取采样周期
List<string> periods = XmlHelper.GetSamplingPeriods();
Console.WriteLine("采样周期：");
foreach (string period in periods)
{
    Console.WriteLine($"  - {period}");
}

// 获取通道配置
List<SampleChannelInfoSet> channels = XmlHelper.GetSampleChannels();
Console.WriteLine($"\n通道数量：{channels.Count}");

// 查找多轴功能通道
var multiAxisChannels = channels.Where(c => c.GroupName == "多轴功能").ToList();
Console.WriteLine($"多轴功能通道数量：{multiAxisChannels.Count}");
foreach (var channel in multiAxisChannels)
{
    Console.WriteLine($"  - {channel.ItemName} (地址: {channel.Address})");
}

// 查找网络通信通道
var networkChannels = channels.Where(c => c.GroupName == "网络通信").ToList();
Console.WriteLine($"网络通信通道数量：{networkChannels.Count}");
foreach (var channel in networkChannels)
{
    Console.WriteLine($"  - {channel.ItemName} (地址: {channel.Address})");
}

// 获取配置信息
string configInfo = XmlHelper.GetCurrentConfigInfo();
Console.WriteLine($"\n当前配置：{configInfo}");
```

### 预期输出
```
采样周期：
  - 50μs
  - 100μs
  - 200μs
  - 500μs
  - 1ms
  - 2ms
  - 5ms
  - 10ms
  - 20ms

通道数量：24
多轴功能通道数量：3
  - 轴间同步误差 (地址: A0)
  - 主轴位置指令 (地址: A1)
  - 从轴跟随误差 (地址: A2)

网络通信通道数量：3
  - EtherCAT状态 (地址: B0)
  - 通信周期计数 (地址: B1)
  - 网络延时 (地址: B2)

当前配置：使用专用配置: Black Tiger
```

## 场景3：未知驱动器测试

### 驱动器信息
- 驱动器名称：UnknownServo
- 软件版本：ServoStudio V1.0.0.0 20220101
- 硬件版本：HW2022.01.01

### 预期结果
- 没有匹配的专用配置
- 使用默认配置
- 包含标准的通道和采样周期

### 测试代码
```csharp
// 设置测试环境
SoftwareStateParameterSet.ServoName = "UnknownServo";
SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V1.0.0.0 20220101";
SoftwareStateParameterSet.ServoHardwareVersion = "HW2022.01.01";

// 重新加载配置
XmlHelper.XmlOscilloscopeOptions();

// 获取采样周期
List<string> periods = XmlHelper.GetSamplingPeriods();
Console.WriteLine("采样周期：");
foreach (string period in periods)
{
    Console.WriteLine($"  - {period}");
}

// 获取通道配置
List<SampleChannelInfoSet> channels = XmlHelper.GetSampleChannels();
Console.WriteLine($"\n通道数量：{channels.Count}");

// 获取配置信息
string configInfo = XmlHelper.GetCurrentConfigInfo();
Console.WriteLine($"\n当前配置：{configInfo}");
```

### 预期输出
```
采样周期：
  - 62.5μs
  - 125μs
  - 250μs
  - 500μs
  - 1ms
  - 2ms
  - 4ms
  - 8ms

通道数量：42
当前配置：使用默认配置
```

## 场景4：空驱动器信息测试

### 驱动器信息
- 驱动器名称：（空）
- 软件版本：（空）
- 硬件版本：（空）

### 预期结果
- 没有匹配的专用配置
- 使用默认配置

### 测试代码
```csharp
// 设置测试环境
SoftwareStateParameterSet.ServoName = "";
SoftwareStateParameterSet.ServoSoftwareVersion = "";
SoftwareStateParameterSet.ServoHardwareVersion = "";

// 重新加载配置
XmlHelper.XmlOscilloscopeOptions();

// 获取配置信息
string configInfo = XmlHelper.GetCurrentConfigInfo();
Console.WriteLine($"当前配置：{configInfo}");

// 获取通道配置
List<SampleChannelInfoSet> channels = XmlHelper.GetSampleChannels();
Console.WriteLine($"通道数量：{channels.Count}");
```

### 预期输出
```
当前配置：使用默认配置
通道数量：42
```

## 完整测试程序

```csharp
public static void RunOscilloscopeConfigTest()
{
    Console.WriteLine("=== 示波器配置系统测试 ===\n");
    
    // 测试场景1：RS60E驱动器
    Console.WriteLine("场景1：RS60E驱动器测试");
    TestRS60EConfig();
    
    Console.WriteLine("\n" + new string('-', 50) + "\n");
    
    // 测试场景2：Black Tiger驱动器
    Console.WriteLine("场景2：Black Tiger驱动器测试");
    TestBlackTigerConfig();
    
    Console.WriteLine("\n" + new string('-', 50) + "\n");
    
    // 测试场景3：未知驱动器
    Console.WriteLine("场景3：未知驱动器测试");
    TestUnknownServoConfig();
    
    Console.WriteLine("\n" + new string('-', 50) + "\n");
    
    // 测试场景4：空驱动器信息
    Console.WriteLine("场景4：空驱动器信息测试");
    TestEmptyServoConfig();
    
    Console.WriteLine("\n=== 测试完成 ===");
}

private static void TestRS60EConfig()
{
    // 设置测试环境
    SoftwareStateParameterSet.ServoName = "RS60E";
    SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V3.0.0.0 20240101";
    SoftwareStateParameterSet.ServoHardwareVersion = "HW2024.01.01";
    
    // 重新加载配置
    XmlHelper.XmlOscilloscopeOptions();
    
    // 获取并显示结果
    DisplayConfigResults();
}

private static void TestBlackTigerConfig()
{
    // 设置测试环境
    SoftwareStateParameterSet.ServoName = "Black Tiger";
    SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V2.5.0.0 20231201";
    SoftwareStateParameterSet.ServoHardwareVersion = "HW2023.12.01";
    
    // 重新加载配置
    XmlHelper.XmlOscilloscopeOptions();
    
    // 获取并显示结果
    DisplayConfigResults();
}

private static void TestUnknownServoConfig()
{
    // 设置测试环境
    SoftwareStateParameterSet.ServoName = "UnknownServo";
    SoftwareStateParameterSet.ServoSoftwareVersion = "ServoStudio V1.0.0.0 20220101";
    SoftwareStateParameterSet.ServoHardwareVersion = "HW2022.01.01";
    
    // 重新加载配置
    XmlHelper.XmlOscilloscopeOptions();
    
    // 获取并显示结果
    DisplayConfigResults();
}

private static void TestEmptyServoConfig()
{
    // 设置测试环境
    SoftwareStateParameterSet.ServoName = "";
    SoftwareStateParameterSet.ServoSoftwareVersion = "";
    SoftwareStateParameterSet.ServoHardwareVersion = "";
    
    // 重新加载配置
    XmlHelper.XmlOscilloscopeOptions();
    
    // 获取并显示结果
    DisplayConfigResults();
}

private static void DisplayConfigResults()
{
    // 获取配置信息
    string configInfo = XmlHelper.GetCurrentConfigInfo();
    Console.WriteLine($"当前配置：{configInfo}");
    
    // 获取采样周期
    List<string> periods = XmlHelper.GetSamplingPeriods();
    Console.WriteLine($"采样周期数量：{periods.Count}");
    Console.WriteLine($"采样周期：{string.Join(", ", periods.Take(5))}...");
    
    // 获取通道配置
    List<SampleChannelInfoSet> channels = XmlHelper.GetSampleChannels();
    Console.WriteLine($"通道数量：{channels.Count}");
    
    // 显示特殊通道
    var specialChannels = channels.Where(c => 
        c.GroupName.Contains("高级功能") || 
        c.GroupName.Contains("多轴功能") || 
        c.GroupName.Contains("网络通信")).ToList();
    
    if (specialChannels.Count > 0)
    {
        Console.WriteLine($"特殊功能通道数量：{specialChannels.Count}");
        foreach (var channel in specialChannels.Take(3))
        {
            Console.WriteLine($"  - {channel.GroupName}: {channel.ItemName}");
        }
    }
}
```

## 运行测试

将上述测试代码添加到项目中，然后调用`RunOscilloscopeConfigTest()`方法即可运行完整的测试套件。测试将验证不同驱动器配置的加载和匹配逻辑是否正确工作。
