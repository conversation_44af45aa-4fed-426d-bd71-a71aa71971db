using System;
using System.Collections.Generic;
using System.IO;
using ServoStudio.GlobalVariable;
using ServoStudio.Models;

namespace ServoStudio.GlobalMethod
{
    /// <summary>
    /// 示波器配置系统测试类
    /// 用于测试和验证配置文件的加载和选择逻辑
    /// </summary>
    public static class OscilloscopeConfigTest
    {
        /// <summary>
        /// 测试配置文件选择逻辑
        /// </summary>
        public static void TestConfigSelection()
        {
            Console.WriteLine("=== 示波器配置文件选择测试 ===");
            
            // 测试场景1: RS60E驱动器
            TestScenario("RS60E", "ServoStudio V3.0.0.0 20240101", "HW2024.01.01");
            
            // 测试场景2: Black Tiger驱动器
            TestScenario("Black Tiger", "ServoStudio V2.5.0.0 20231201", "HW2023.12.01");
            
            // 测试场景3: 未知驱动器
            TestScenario("UnknownServo", "ServoStudio V1.0.0.0 20220101", "HW2022.01.01");
            
            // 测试场景4: 空驱动器信息
            TestScenario("", "", "");
        }
        
        /// <summary>
        /// 测试特定场景
        /// </summary>
        private static void TestScenario(string servoName, string softwareVersion, string hardwareVersion)
        {
            Console.WriteLine($"\n--- 测试场景: {servoName} ---");
            
            // 设置测试环境
            SoftwareStateParameterSet.ServoName = servoName;
            SoftwareStateParameterSet.ServoSoftwareVersion = softwareVersion;
            SoftwareStateParameterSet.ServoHardwareVersion = hardwareVersion;
            
            // 获取配置文件路径
            string configPath = OscilloscopeConfigManager.GetConfigFilePath();
            string configInfo = OscilloscopeConfigManager.GetConfigFileInfo();
            
            Console.WriteLine($"驱动器名称: {servoName}");
            Console.WriteLine($"软件版本: {softwareVersion}");
            Console.WriteLine($"硬件版本: {hardwareVersion}");
            Console.WriteLine($"选择的配置文件: {Path.GetFileName(configPath)}");
            Console.WriteLine($"配置信息: {configInfo}");
            Console.WriteLine($"文件是否存在: {File.Exists(configPath)}");
            
            // 验证配置文件
            bool isValid = OscilloscopeConfigManager.ValidateConfigFile(configPath);
            Console.WriteLine($"配置文件有效性: {isValid}");
        }
        
        /// <summary>
        /// 测试配置文件生成功能
        /// </summary>
        public static void TestConfigGeneration()
        {
            Console.WriteLine("\n=== 配置文件生成测试 ===");
            
            try
            {
                // 测试1: 生成基本配置模板
                Console.WriteLine("\n1. 生成基本配置模板...");
                bool result1 = OscilloscopeConfigTemplate.GenerateConfigTemplate("TestServo1");
                Console.WriteLine($"生成结果: {result1}");
                
                // 测试2: 生成带版本的配置模板
                Console.WriteLine("\n2. 生成带版本的配置模板...");
                bool result2 = OscilloscopeConfigTemplate.GenerateConfigTemplate("TestServo2", "1000");
                Console.WriteLine($"生成结果: {result2}");
                
                // 测试3: 生成自定义通道配置
                Console.WriteLine("\n3. 生成自定义通道配置...");
                List<SampleChannelInfoSet> customChannels = new List<SampleChannelInfoSet>
                {
                    new SampleChannelInfoSet { GroupName = "测试", ID = 1, ItemName = "测试通道1", Address = "A0" },
                    new SampleChannelInfoSet { GroupName = "测试", ID = 2, ItemName = "测试通道2", Address = "A1" }
                };
                bool result3 = OscilloscopeConfigTemplate.GenerateConfigTemplate("TestServo3", null, customChannels);
                Console.WriteLine($"生成结果: {result3}");
                
                // 测试4: 复制并修改配置
                Console.WriteLine("\n4. 复制并修改配置...");
                string sourcePath = Path.Combine(System.Environment.CurrentDirectory, "Xml", "OscilloscopeOptions.xml");
                Dictionary<string, string> addressMapping = new Dictionary<string, string>
                {
                    {"03", "30"},
                    {"07", "40"}
                };
                bool result4 = OscilloscopeConfigTemplate.CopyAndModifyConfig(sourcePath, "TestServo4", addressMapping);
                Console.WriteLine($"复制修改结果: {result4}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试配置文件验证功能
        /// </summary>
        public static void TestConfigValidation()
        {
            Console.WriteLine("\n=== 配置文件验证测试 ===");
            
            // 获取所有可用的配置文件
            List<ConfigFileInfo> configFiles = OscilloscopeConfigManager.GetAvailableConfigFiles();
            
            Console.WriteLine($"找到 {configFiles.Count} 个配置文件:");
            
            foreach (var configFile in configFiles)
            {
                Console.WriteLine($"\n文件: {configFile.FileName}");
                Console.WriteLine($"显示名称: {configFile.DisplayName}");
                Console.WriteLine($"是否默认: {configFile.IsDefault}");
                
                // 验证文件
                bool isValid = OscilloscopeConfigManager.ValidateConfigFile(configFile.FilePath);
                Console.WriteLine($"验证结果: {isValid}");
                
                if (!isValid)
                {
                    Console.WriteLine("尝试修复配置文件...");
                    bool repaired = OscilloscopeConfigTemplate.ValidateAndRepairConfig(configFile.FilePath);
                    Console.WriteLine($"修复结果: {repaired}");
                }
            }
        }
        
        /// <summary>
        /// 运行完整测试套件
        /// </summary>
        public static void RunFullTest()
        {
            Console.WriteLine("开始示波器配置系统完整测试...\n");
            
            try
            {
                // 1. 测试配置文件选择
                TestConfigSelection();
                
                // 2. 测试配置文件生成
                TestConfigGeneration();
                
                // 3. 测试配置文件验证
                TestConfigValidation();
                
                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生严重错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 清理测试生成的文件
        /// </summary>
        public static void CleanupTestFiles()
        {
            Console.WriteLine("\n=== 清理测试文件 ===");
            
            string xmlDir = Path.Combine(System.Environment.CurrentDirectory, "Xml");
            if (!Directory.Exists(xmlDir)) return;
            
            string[] testFiles = Directory.GetFiles(xmlDir, "OscilloscopeOptions_TestServo*.xml");
            
            foreach (string file in testFiles)
            {
                try
                {
                    File.Delete(file);
                    Console.WriteLine($"已删除: {Path.GetFileName(file)}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"删除文件失败 {Path.GetFileName(file)}: {ex.Message}");
                }
            }
            
            Console.WriteLine($"清理完成，共删除 {testFiles.Length} 个测试文件");
        }
        
        /// <summary>
        /// 生成测试报告
        /// </summary>
        public static void GenerateTestReport()
        {
            Console.WriteLine("\n=== 生成测试报告 ===");
            
            string reportPath = Path.Combine(System.Environment.CurrentDirectory, "OscilloscopeConfigTestReport.txt");
            
            try
            {
                using (StreamWriter writer = new StreamWriter(reportPath, false, System.Text.Encoding.UTF8))
                {
                    writer.WriteLine("示波器配置系统测试报告");
                    writer.WriteLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    writer.WriteLine(new string('=', 50));
                    
                    // 系统信息
                    writer.WriteLine("\n系统信息:");
                    writer.WriteLine($"当前目录: {System.Environment.CurrentDirectory}");
                    writer.WriteLine($"XML目录: {Path.Combine(System.Environment.CurrentDirectory, "Xml")}");
                    
                    // 配置文件列表
                    writer.WriteLine("\n可用配置文件:");
                    List<ConfigFileInfo> configFiles = OscilloscopeConfigManager.GetAvailableConfigFiles();
                    foreach (var config in configFiles)
                    {
                        writer.WriteLine($"- {config.FileName} ({config.DisplayName})");
                    }
                    
                    // 当前配置
                    writer.WriteLine("\n当前配置:");
                    writer.WriteLine($"驱动器名称: {SoftwareStateParameterSet.ServoName}");
                    writer.WriteLine($"软件版本: {SoftwareStateParameterSet.ServoSoftwareVersion}");
                    writer.WriteLine($"硬件版本: {SoftwareStateParameterSet.ServoHardwareVersion}");
                    writer.WriteLine($"选择的配置: {OscilloscopeConfigManager.GetConfigFileInfo()}");
                }
                
                Console.WriteLine($"测试报告已生成: {reportPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成测试报告失败: {ex.Message}");
            }
        }
    }
}
