using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalVariable;
using ServoStudio.Models;

namespace ServoStudio.GlobalMethod
{
    /// <summary>
    /// 示波器配置管理器
    /// 负责根据驱动器类型和版本加载对应的配置文件
    /// </summary>
    public static class OscilloscopeConfigManager
    {
        /// <summary>
        /// 获取当前驱动器对应的示波器配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public static string GetConfigFilePath()
        {
            try
            {
                string baseDir = System.Environment.CurrentDirectory + "\\Xml\\";
                
                // 获取驱动器信息
                string servoName = SoftwareStateParameterSet.ServoName?.Trim();
                string softwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion?.Trim();
                string hardwareVersion = SoftwareStateParameterSet.ServoHardwareVersion?.Trim();
                
                // 配置文件查找优先级
                List<string> candidatePaths = new List<string>();
                
                // 1. 驱动器名称 + 软件版本
                if (!string.IsNullOrEmpty(servoName) && !string.IsNullOrEmpty(softwareVersion) && softwareVersion.Length >= 13)
                {
                    string versionPrefix = softwareVersion.Substring(9, 4);
                    candidatePaths.Add(baseDir + $"OscilloscopeOptions_{servoName}_V{versionPrefix}.xml");
                }
                
                // 2. 驱动器名称 + 硬件版本
                if (!string.IsNullOrEmpty(servoName) && !string.IsNullOrEmpty(hardwareVersion) && hardwareVersion.Length >= 8)
                {
                    string hwVersionPrefix = hardwareVersion.Substring(0, 4);
                    candidatePaths.Add(baseDir + $"OscilloscopeOptions_{servoName}_HW{hwVersionPrefix}.xml");
                }
                
                // 3. 仅驱动器名称
                if (!string.IsNullOrEmpty(servoName))
                {
                    candidatePaths.Add(baseDir + $"OscilloscopeOptions_{servoName}.xml");
                }
                
                // 4. 仅软件版本
                if (!string.IsNullOrEmpty(softwareVersion) && softwareVersion.Length >= 13)
                {
                    string versionPrefix = softwareVersion.Substring(9, 4);
                    candidatePaths.Add(baseDir + $"OscilloscopeOptions_V{versionPrefix}.xml");
                }
                
                // 5. 默认配置文件
                candidatePaths.Add(GlobalConstant.FilePath.XmlOptionsPath);
                
                // 查找第一个存在的配置文件
                foreach (string path in candidatePaths)
                {
                    if (File.Exists(path))
                    {
                        return path;
                    }
                }
                
                // 如果都不存在，返回默认路径
                return GlobalConstant.FilePath.XmlOptionsPath;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetConfigFilePath", ex);
                return GlobalConstant.FilePath.XmlOptionsPath;
            }
        }
        
        /// <summary>
        /// 获取配置文件信息
        /// </summary>
        /// <returns>配置文件信息字符串</returns>
        public static string GetConfigFileInfo()
        {
            try
            {
                string configPath = GetConfigFilePath();
                string fileName = Path.GetFileName(configPath);
                
                if (fileName.Equals("OscilloscopeOptions.xml", StringComparison.OrdinalIgnoreCase))
                {
                    return "使用默认配置";
                }
                else if (fileName.Contains("_"))
                {
                    // 解析配置文件名称
                    string[] parts = fileName.Replace("OscilloscopeOptions_", "").Replace(".xml", "").Split('_');
                    if (parts.Length >= 1)
                    {
                        return $"使用专用配置: {string.Join(" ", parts)}";
                    }
                }
                
                return $"使用配置文件: {fileName}";
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetConfigFileInfo", ex);
                return "配置文件信息获取失败";
            }
        }
        
        /// <summary>
        /// 验证配置文件是否有效
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>是否有效</returns>
        public static bool ValidateConfigFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }
                
                XmlDocument doc = new XmlDocument();
                doc.Load(filePath);
                
                // 检查必要的节点是否存在
                XmlNode rootNode = doc.SelectSingleNode("OscilloscopeOptions");
                if (rootNode == null) return false;
                
                XmlNode samplingPeriodsNode = rootNode.SelectSingleNode("SamplingPeriods");
                if (samplingPeriodsNode == null) return false;
                
                XmlNode sampleChannelsNode = rootNode.SelectSingleNode("SampleChannels");
                if (sampleChannelsNode == null) return false;
                
                // 检查是否有采样周期配置
                XmlNodeList periodNodes = samplingPeriodsNode.SelectNodes("Period");
                if (periodNodes == null || periodNodes.Count == 0) return false;
                
                // 检查是否有通道配置
                XmlNodeList channelNodes = sampleChannelsNode.SelectNodes("Channel");
                if (channelNodes == null || channelNodes.Count == 0) return false;
                
                return true;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "ValidateConfigFile", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        /// <param name="targetPath">目标路径</param>
        /// <returns>是否成功</returns>
        public static bool CreateDefaultConfigFile(string targetPath)
        {
            try
            {
                string defaultPath = GlobalConstant.FilePath.XmlOptionsPath;
                if (File.Exists(defaultPath))
                {
                    File.Copy(defaultPath, targetPath, true);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "CreateDefaultConfigFile", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 获取所有可用的配置文件列表
        /// </summary>
        /// <returns>配置文件信息列表</returns>
        public static List<ConfigFileInfo> GetAvailableConfigFiles()
        {
            List<ConfigFileInfo> configFiles = new List<ConfigFileInfo>();
            
            try
            {
                string baseDir = System.Environment.CurrentDirectory + "\\Xml\\";
                if (!Directory.Exists(baseDir)) return configFiles;
                
                string[] xmlFiles = Directory.GetFiles(baseDir, "OscilloscopeOptions*.xml");
                
                foreach (string filePath in xmlFiles)
                {
                    if (ValidateConfigFile(filePath))
                    {
                        string fileName = Path.GetFileName(filePath);
                        ConfigFileInfo info = new ConfigFileInfo
                        {
                            FilePath = filePath,
                            FileName = fileName,
                            DisplayName = GetDisplayNameFromFileName(fileName),
                            IsDefault = fileName.Equals("OscilloscopeOptions.xml", StringComparison.OrdinalIgnoreCase)
                        };
                        configFiles.Add(info);
                    }
                }
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "GetAvailableConfigFiles", ex);
            }
            
            return configFiles;
        }
        
        /// <summary>
        /// 从文件名获取显示名称
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>显示名称</returns>
        private static string GetDisplayNameFromFileName(string fileName)
        {
            if (fileName.Equals("OscilloscopeOptions.xml", StringComparison.OrdinalIgnoreCase))
            {
                return "默认配置";
            }
            
            string baseName = fileName.Replace("OscilloscopeOptions_", "").Replace(".xml", "");
            return baseName.Replace("_", " ");
        }
    }
    
    /// <summary>
    /// 配置文件信息
    /// </summary>
    public class ConfigFileInfo
    {
        public string FilePath { get; set; }
        public string FileName { get; set; }
        public string DisplayName { get; set; }
        public bool IsDefault { get; set; }
    }
}
