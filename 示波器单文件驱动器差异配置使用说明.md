# 示波器单文件驱动器差异配置使用说明

## 概述

为了支持不同驱动器的采集通道差异，系统现在支持在单个XML文件中配置多种驱动器的采样通道和采样周期。系统会根据当前连接的驱动器信息自动选择最合适的配置。

## 配置文件结构

```xml
<?xml version="1.0" encoding="utf-8" ?>
<OscilloscopeOptions>
  <!-- 默认配置 - 当没有找到匹配的驱动器配置时使用 -->
  <DefaultConfig>
    <SamplingPeriods>
      <Period>62.5μs</Period>
      <!-- 更多采样周期... -->
    </SamplingPeriods>
    <SampleChannels>
      <Channel GroupName="位置" ID="1" ItemName="位置指令" Address="03" />
      <!-- 更多通道配置... -->
    </SampleChannels>
  </DefaultConfig>

  <!-- 驱动器专用配置 -->
  <ServoConfigs>
    <!-- RS60E驱动器配置 -->
    <ServoConfig Name="RS60E" SoftwareVersion="V3.*" HardwareVersion="HW2024.*">
      <SamplingPeriods>
        <Period>31.25μs</Period>
        <!-- 更多采样周期... -->
      </SamplingPeriods>
      <SampleChannels>
        <InheritDefault>true</InheritDefault>
        <Channel GroupName="Debug" ID="42" ItemName="RS60E专用参数1" Address="2A" />
        <!-- 更多通道配置... -->
      </SampleChannels>
    </ServoConfig>
    
    <!-- Black Tiger驱动器配置 -->
    <ServoConfig Name="Black Tiger" SoftwareVersion="V2.*" HardwareVersion="HW2023.*">
      <SamplingPeriods>
        <Period>50μs</Period>
        <!-- 更多采样周期... -->
      </SamplingPeriods>
      <SampleChannels>
        <InheritDefault>false</InheritDefault>
        <Channel GroupName="位置" ID="1" ItemName="位置指令" Address="30" />
        <!-- 更多通道配置... -->
      </SampleChannels>
    </ServoConfig>
  </ServoConfigs>
</OscilloscopeOptions>
```

## 配置项说明

### DefaultConfig（默认配置）

- 当没有找到匹配的驱动器配置时使用
- 包含基础的采样周期和通道配置

### ServoConfigs（驱动器专用配置）

- 包含多个驱动器的专用配置
- 每个配置通过Name、SoftwareVersion和HardwareVersion属性进行匹配

### ServoConfig（单个驱动器配置）

- **Name**: 驱动器名称，支持精确匹配，`*`表示匹配任何驱动器
- **SoftwareVersion**: 软件版本，支持通配符，如`V3.*`表示匹配所有V3.x版本
- **HardwareVersion**: 硬件版本，支持通配符，如`HW2024.*`表示匹配所有2024年的硬件版本

### SamplingPeriods（采样周期）

- `<Period>`: 定义可用的采样周期
- 支持μs（微秒）和ms（毫秒）单位
- 不同驱动器可以有不同的采样周期选项

### SampleChannels（采样通道）

- `<InheritDefault>`: 是否继承默认配置中的通道
  - `true`: 继承默认通道，并可以添加或覆盖特定通道
  - `false`: 不继承默认通道，完全使用自定义通道配置
- `<Channel>`: 通道配置
  - `GroupName`: 通道分组名称（如"位置"、"速度"、"转矩"等）
  - `ID`: 通道唯一标识符
  - `ItemName`: 通道显示名称
  - `Address`: 通道对应的硬件地址（十六进制）

## 配置匹配规则

系统按以下步骤选择配置：

1. 读取当前连接的驱动器信息（名称、软件版本、硬件版本）
2. 遍历所有ServoConfig节点，寻找匹配的配置
3. 匹配规则：
   - 驱动器名称必须精确匹配（除非配置中使用`*`）
   - 软件版本和硬件版本支持通配符匹配
   - 如果多个配置匹配，使用第一个匹配的配置
4. 如果没有找到匹配的配置，使用DefaultConfig

## 通配符规则

- `*`: 匹配任意字符序列
- 示例：
  - `V3.*`: 匹配所有V3.x版本，如V3.0.0.0、V3.1.2.3等
  - `HW2024.*`: 匹配所有2024年的硬件版本
  - `*`: 匹配任何值

## 实际应用示例

### 示例1：RS60E驱动器配置

```xml
<ServoConfig Name="RS60E" SoftwareVersion="V3.*" HardwareVersion="HW2024.*">
  <SamplingPeriods>
    <Period>31.25μs</Period>
    <Period>62.5μs</Period>
    <Period>125μs</Period>
    <Period>250μs</Period>
    <Period>500μs</Period>
    <Period>1ms</Period>
    <Period>2ms</Period>
    <Period>4ms</Period>
    <Period>8ms</Period>
    <Period>16ms</Period>
  </SamplingPeriods>
  <SampleChannels>
    <InheritDefault>true</InheritDefault>
    <Channel GroupName="Debug" ID="42" ItemName="RS60E专用参数1" Address="2A" />
    <Channel GroupName="Debug" ID="43" ItemName="RS60E专用参数2" Address="2B" />
    <Channel GroupName="高级功能" ID="44" ItemName="振动抑制输出" Address="2C" />
    <Channel GroupName="高级功能" ID="45" ItemName="自适应增益输出" Address="2D" />
    <Channel GroupName="高级功能" ID="46" ItemName="负载惯量估算值" Address="2E" />
  </SampleChannels>
</ServoConfig>
```

特点：

- 继承默认通道配置
- 添加RS60E特有的高级功能通道
- 支持高速采样（31.25μs）

### 示例2：Black Tiger驱动器配置

```xml
<ServoConfig Name="Black Tiger" SoftwareVersion="V2.*" HardwareVersion="HW2023.*">
  <SamplingPeriods>
    <Period>50μs</Period>
    <Period>100μs</Period>
    <Period>200μs</Period>
    <Period>500μs</Period>
    <Period>1ms</Period>
    <Period>2ms</Period>
    <Period>5ms</Period>
    <Period>10ms</Period>
    <Period>20ms</Period>
  </SamplingPeriods>
  <SampleChannels>
    <InheritDefault>false</InheritDefault>
    <Channel GroupName="停用" ID="0" ItemName="停用" Address="" />
    <Channel GroupName="位置" ID="1" ItemName="位置指令" Address="30" />
    <Channel GroupName="位置" ID="2" ItemName="滤波后位置指令" Address="31" />
    <Channel GroupName="位置" ID="3" ItemName="位置反馈" Address="32" />
    <Channel GroupName="位置" ID="4" ItemName="位置差值" Address="33" />
    <Channel GroupName="速度" ID="7" ItemName="位置环输出速度指令" Address="40" />
    <Channel GroupName="速度" ID="8" ItemName="速度指令" Address="41" />
    <Channel GroupName="速度" ID="10" ItemName="速度反馈" Address="43" />
    <Channel GroupName="速度" ID="13" ItemName="速度差值" Address="46" />
    <Channel GroupName="转矩" ID="14" ItemName="速度环输出转矩指令" Address="50" />
    <Channel GroupName="转矩" ID="15" ItemName="转矩指令" Address="51" />
    <Channel GroupName="转矩" ID="16" ItemName="转矩反馈" Address="52" />
    <Channel GroupName="转矩" ID="17" ItemName="U相电流" Address="53" />
    <Channel GroupName="转矩" ID="18" ItemName="V相电流" Address="54" />
    <Channel GroupName="转矩" ID="19" ItemName="W相电流" Address="55" />
    <Channel GroupName="母线" ID="31" ItemName="母线电压" Address="80" />
    <Channel GroupName="母线" ID="32" ItemName="母线电流" Address="81" />
    <Channel GroupName="多轴功能" ID="38" ItemName="轴间同步误差" Address="A0" />
    <Channel GroupName="多轴功能" ID="39" ItemName="主轴位置指令" Address="A1" />
    <Channel GroupName="多轴功能" ID="40" ItemName="从轴跟随误差" Address="A2" />
    <Channel GroupName="网络通信" ID="42" ItemName="EtherCAT状态" Address="B0" />
    <Channel GroupName="网络通信" ID="43" ItemName="通信周期计数" Address="B1" />
    <Channel GroupName="网络通信" ID="44" ItemName="网络延时" Address="B2" />
  </SampleChannels>
</ServoConfig>
```

特点：

- 不继承默认通道，使用完全自定义的地址映射
- 包含多轴功能和网络通信特有通道
- 使用不同的采样周期选项

## 配置文件维护

### 添加新驱动器配置

1. 打开`OscilloscopeOptions.xml`文件
2. 在`<ServoConfigs>`节点下添加新的`<ServoConfig>`节点
3. 设置驱动器名称、软件版本和硬件版本匹配规则
4. 配置采样周期和通道信息
5. 保存文件

### 修改现有配置

1. 打开`OscilloscopeOptions.xml`文件
2. 找到对应的`<ServoConfig>`节点
3. 修改采样周期或通道信息
4. 保存文件

### 配置优先级

如果多个配置匹配当前驱动器，系统会使用第一个匹配的配置。因此，建议将更具体的配置放在前面，通用配置放在后面。

## 注意事项

1. **XML格式**: 确保XML格式正确，否则可能导致配置无法加载
2. **ID唯一性**: 每个通道的ID必须唯一，否则后面的配置会覆盖前面的配置
3. **通配符使用**: 通配符匹配是基于正则表达式实现的，确保使用正确的格式
4. **继承关系**: 使用`<InheritDefault>true</InheritDefault>`可以继承默认配置，减少重复配置
5. **地址格式**: 通道地址使用十六进制格式，不需要"0x"前缀

## 故障排除

### 问题1：配置不生效

- 检查驱动器名称、软件版本和硬件版本是否正确
- 确认XML格式是否正确
- 检查通配符格式是否正确

### 问题2：通道显示异常

- 检查通道ID是否重复
- 确认通道地址是否正确
- 验证GroupName和ItemName是否为空

### 问题3：采样周期选项缺失

- 检查SamplingPeriods节点是否存在
- 确认Period格式是否正确（包含单位）

## 总结

通过在单个XML文件中配置多种驱动器的差异，可以更灵活地支持不同驱动器的特性，提高系统的可维护性和扩展性。系统会根据当前连接的驱动器信息自动选择最合适的配置，无需用户手动切换。

相比多文件方案，单文件方案具有以下优势：

1. **集中管理**: 所有配置都在一个文件中，便于维护和版本控制
2. **减少文件数量**: 避免创建大量配置文件
3. **配置继承**: 支持继承默认配置，减少重复配置
4. **灵活匹配**: 支持通配符匹配，可以基于版本范围进行配置
5. **向后兼容**: 保持与现有系统的兼容性
